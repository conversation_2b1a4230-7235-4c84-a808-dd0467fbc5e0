import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";
import { Prisma } from "@prisma/client";

type ContentWithRelations = {
  id: string;
  weeklyActivityId: string;
  activityDate: Date;
  contentType: string;
  channel: string | null;
  details: string | null;
  destination: string;
  status: string | null;
  priority: string | null;
  position: number | null;
  caption: string | null;
  copywriting: string | null;
  reference: string | null;
  urlStructuringFeed: string[];
  urlTypes: string[];
  urlFolder: string | null;
  archived: boolean;
  assignedToId: string | null;
  review: string | null;
  weeklyActivity?: {
    monthlyPlanning?: {
      client?: {
        id: string;
        name: string;
      };
    };
  };
  assignedTo: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
    role?: string;
  } | null;
  steps: Array<{
    id: string;
    type: string;
    assignedTo: {
      id: string;
      name: string | null;
      email: string;
      image: string | null;
    };
  }>;
};

type GeneralDemandWithRelations = {
  id: string;
  title: string;
  description: string | null;
  dueDate: Date | null;
  priority: string;
  status: string;
  position: number | null;
  urlStructuringFeed: string[];
  urlTypes: string[];
  urlFolder: string | null;
  clientId: string | null;
  looseClientId: string | null;
  archived: boolean;
  review: string | null;
  client: {
    id: string;
    name: string;
  } | null;
  looseClient: {
    id: string;
    name: string;
  } | null;
  assignedTo: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
    role?: string;
  } | null;
};

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user || (user.role !== "ADMIN" && user.role !== "DEVELOPER")) {
      return NextResponse.json({ error: "Permissão negada" }, { status: 403 });
    }

    const url = new URL(request.url);
    const type = url.searchParams.get("type") || "all";
    const showArchived = url.searchParams.get("showArchived") === "true";

    const contentQuery: Prisma.ContentFindManyArgs = {
      select: {
        id: true,
        weeklyActivityId: true,
        activityDate: true,
        contentType: true,
        channel: true,
        details: true,
        destination: true,
        status: true,
        priority: true,
        position: true,
        caption: true,
        copywriting: true,
        reference: true,
        urlStructuringFeed: true,
        urlTypes: true,
        urlFolder: true,
        archived: true,
        assignedToId: true,
        review: true,
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
          },
        },
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: true,
              },
            },
          },
        },
        steps: {
          include: {
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
      where: {},
    };

    contentQuery.where = { 
      archived: showArchived ? true : false 
    };

    const contents = type === "general"
      ? []
      : await prisma.content.findMany(contentQuery);

    const generalDemandQuery: Prisma.GeneralDemandFindManyArgs = {
      select: {
        id: true,
        title: true,
        description: true,
        dueDate: true,
        priority: true,
        status: true,
        position: true,
        urlStructuringFeed: true,
        urlTypes: true,
        urlFolder: true,
        clientId: true,
        looseClientId: true,
        archived: true,
        review: true,
        client: true,
        looseClient: true,
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
      where: {},
    };

    generalDemandQuery.where = { 
      archived: showArchived ? true : false 
    };

    const generalDemands = type === "content"
      ? []
      : await prisma.generalDemand.findMany(generalDemandQuery);

    const transformedContents = contents.map((content) => {
      const typedContent = content as unknown as ContentWithRelations;

      const client = typedContent.weeklyActivity?.monthlyPlanning?.client || {
        id: "none",
        name: "Cliente não encontrado"
      };

      return {
        id: typedContent.id,
        type: "content" as const,
        activityDate: typedContent.activityDate.toISOString(),
        contentType: typedContent.contentType,
        channel: typedContent.channel,
        details: typedContent.details,
        destination: typedContent.destination,
        status: typedContent.status,
        priority: typedContent.priority,
        position: typedContent.position,
        caption: typedContent.caption,
        copywriting: typedContent.copywriting,
        reference: typedContent.reference,
        urlStructuringFeed: typedContent.urlStructuringFeed,
        urlTypes: typedContent.urlTypes,
        urlFolder: typedContent.urlFolder,
        client: client,
        weeklyActivityId: typedContent.weeklyActivityId,
        assignedTo: typedContent.assignedTo,
        steps: typedContent.steps,
        archived: typedContent.archived,
        review: typedContent.review,
      };
    });

    const transformedGeneralDemands = generalDemands.map((demand) => {
      const typedDemand = demand as unknown as GeneralDemandWithRelations;

      return {
        id: typedDemand.id,
        type: "general" as const,
        activityDate: typedDemand.dueDate?.toISOString() || new Date().toISOString(),
        title: typedDemand.title,
        description: typedDemand.description,
        priority: typedDemand.priority,
        position: typedDemand.position,
        status: typedDemand.status,
        urlStructuringFeed: typedDemand.urlStructuringFeed,
        urlTypes: typedDemand.urlTypes,
        urlFolder: typedDemand.urlFolder,
        client: typedDemand.client || { id: "none", name: "Cliente desconhecido" },
        assignedTo: typedDemand.assignedTo,
        isLooseClient: !typedDemand.clientId && !!typedDemand.looseClientId,
        looseClientName: typedDemand.looseClient?.name,
        archived: typedDemand.archived,
        review: typedDemand.review,
      };
    });

    const result = [...transformedContents, ...transformedGeneralDemands];

    return NextResponse.json(result);
  } catch (error) {
    console.error("Erro ao buscar demandas:", error);
    return NextResponse.json(
      { error: "Erro ao processar a requisição" },
      { status: 500 }
    );
  }
}
