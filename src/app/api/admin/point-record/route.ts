import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const pointRecords = await prisma.pointRecord.findMany({
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        const formattedRecords = pointRecords.map(record => ({
            id: record.id,
            userId: record.userId,
            userName: record.user.name || 'Usuário',
            userEmail: record.user.email,
            clockIn: record.clockIn?.toISOString(),
            clockOut: record.clockOut?.toISOString(),
            date: record.date.toISOString().split('T')[0],
            totalHours: record.totalHours,
            timeBank: record.timeBank,
            createdAt: record.createdAt.toISOString()
        }));

        return NextResponse.json(formattedRecords);
    } catch (error) {
        console.error("Erro ao buscar registros de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { userId, clockIn, clockOut, date } = await request.json();

        if (!userId || !date) {
            return NextResponse.json({ error: "Dados obrigatórios não fornecidos" }, { status: 400 });
        }

        const targetUser = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!targetUser) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        let totalHours = null;
        let timeBank = 0;
        if (clockIn && clockOut) {
            const clockInDate = new Date(clockIn);
            const clockOutDate = new Date(clockOut);
            totalHours = (clockOutDate.getTime() - clockInDate.getTime()) / (1000 * 60 * 60);
            timeBank = totalHours > 8.8 ? totalHours - 8.8 : 0;
        }

        const existingRecord = await prisma.pointRecord.findFirst({
            where: {
                userId,
                date: new Date(date)
            }
        });

        if (existingRecord) {
            return NextResponse.json({ error: "Já existe um registro para este usuário nesta data" }, { status: 400 });
        }

        const pointRecord = await prisma.pointRecord.create({
            data: {
                userId,
                clockIn: clockIn ? new Date(clockIn) : null,
                clockOut: clockOut ? new Date(clockOut) : null,
                date: (() => {
                    const [year, month, day] = date.split('-').map(Number);
                    return new Date(year, month - 1, day);
                })(),
                totalHours,
                timeBank
            },
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            }
        });

        const formattedRecord = {
            id: pointRecord.id,
            userId: pointRecord.userId,
            userName: pointRecord.user.name || 'Usuário',
            userEmail: pointRecord.user.email,
            clockIn: pointRecord.clockIn?.toISOString(),
            clockOut: pointRecord.clockOut?.toISOString(),
            date: pointRecord.date.toISOString().split('T')[0],
            totalHours: pointRecord.totalHours,
            createdAt: pointRecord.createdAt.toISOString()
        };

        return NextResponse.json(formattedRecord, { status: 201 });
    } catch (error) {
        console.error("Erro ao criar registro de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}

