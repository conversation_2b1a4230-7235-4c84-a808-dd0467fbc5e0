"use client";

import { Calendar, ChartNoAxesGantt, ChevronsUpDown, CirclePlus, FileChartColumn, Grid3x3, History, LayoutDashboard, LayoutList, ListTodo, MoveUpRight, NotepadText, Users, X } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Client as ImportedClient } from "../clients/page";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "../components/header";
import { Footer } from "../components/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Button } from "../components/ui/button";
import Loading from "../components/ui/loading";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../components/ui/dialog";
import { ClientNavigationModal } from "../components/client-navigation-modal";
import { CreatePlanning } from "../components/create-planning";
import { ResultsReport } from "@prisma/client";
import { Separator } from "../components/ui/separator";
import { FeedStructuringTasks } from "../components/feed-structuring-tasks";
import { UserLatestDemands } from "../components/user-latest-demands";
import { UserDemandsStats } from "../components/user-demands-stats";
import { TotalPendingDemands } from "../components/total-pending-demands";
import { WelcomeAlert } from "@/app/components/welcome-alert";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../components/ui/collapsible";
import { UpdatesInformationAdmin } from "../components/updates-information-admin";
import { UpdatesInformationOthers } from "../components/updates-information-others";
import { ClientSearchCommand } from "../components/client-search-command";
import { PointClock } from "../components/point-clock";

interface MonthlyPlanning {
    id: string;
    month: string | number;
    year: number;
    clientName?: string;
    clientId?: string;
    createdAt?: string;
}

interface Client extends Omit<ImportedClient, 'monthlyPlannings'> {
    id: string;
    name: string;
    monthlyPlannings?: MonthlyPlanning[];
    resultsReport?: ResultsReport[];
}

const getUserPermissions = (role: string, accessLevel: string) => {
    const accessLevels = {
        'VIEWER': 1,
        'EDITOR': 2,
        'ADMIN': 3
    };

    const numericLevel = accessLevels[accessLevel as keyof typeof accessLevels] || 1;

    return {
        canAccessDashboard: ['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER'].includes(role),
        canCreatePlanning:
            ['ADMIN', 'DEVELOPER'].includes(role) ||
            (['COPY', 'DESIGNER'].includes(role) && numericLevel > 2),
        canAccessFeedTasks: role === 'DESIGNER',
        isDesigner: role === 'DESIGNER',
        isCopy: role === 'COPY',
        isContentCreator: role === 'DESIGNER' || role === 'COPY',
        isAdmin: role === 'ADMIN' || role === 'DEVELOPER'
    };
};

export default function Dashboard() {
    const { data: session, status } = useSession();
    const [clients, setClients] = useState<Client[]>([]);
    const [allClients, setAllClients] = useState<Client[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [currentTime, setCurrentTime] = useState(new Date());
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [showInformation, setShowInformation] = useState(true);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const router = useRouter();
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const [userPermissions, setUserPermissions] = useState({
        canAccessDashboard: false,
        canCreatePlanning: false,
        canAccessFeedTasks: false,
        isDesigner: false,
        isCopy: false,
        isContentCreator: false,
        isAdmin: false
    });

    useEffect(() => {
        const storedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");
        setViewedClients(storedClients);
    }, []);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const refreshSession = async () => {
            if (status === "authenticated") {
                await fetch('/api/auth/session');
            }
        };

        refreshSession();
    }, [status]);

    useEffect(() => {
        if (status === "authenticated") {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            fetch(`/api/clients?include=resultsReport&month=${currentMonth}&year=${currentYear}`)
                .then((res) => res.json())
                .then((data) => {
                    setClients(data);
                })
                .finally(() => setIsLoading(false));

            fetch(`/api/clients?include=resultsReport`)
                .then((res) => res.json())
                .then((data) => {
                    setAllClients(data);
                });

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            setUserPermissions(getUserPermissions(user?.role || '', user?.accessLevel || 'VIEWER'));
                        } else {
                            console.error("Erro ao buscar função do usuário:", await response.json());
                        }
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        }
    }, [status, session]);

    const refreshData = () => {
        setIsLoading(true);
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();

        fetch(`/api/clients?include=resultsReport&month=${currentMonth}&year=${currentYear}`)
            .then((res) => res.json())
            .then((data) => {
                setClients(data);
            })
            .finally(() => setIsLoading(false));

        fetch(`/api/clients?include=resultsReport`)
            .then((res) => res.json())
            .then((data) => {
                setAllClients(data);
            });
    };

    const handleClose = () => {
        setDialogOpen(false);
    };

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        const storedShowInformation = localStorage.getItem("showInformation");
        if (storedShowInformation !== null) {
            setShowInformation(JSON.parse(storedShowInformation));
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("showInformation", JSON.stringify(showInformation));
    }, [showInformation]);

    const formattedDate = currentTime.toLocaleDateString("pt-BR", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
    });

    const currentMonth = currentTime.toLocaleString("pt-BR", { month: "long" });

    if (isFetchingRole || isLoading) {
        return (
            <>
                <Header />
                <div className="min-h-[80vh] flex flex-col justify-center items-center">
                    <Loading />
                </div>
                <Footer />
            </>
        );
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                <div className="flex flex-col-reverse lg:flex-row justify-between gap-6">
                    <div className="flex flex-col items-start gap-4 flex-grow">
                        <div className="flex items-center gap-2 group">
                            <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                <LayoutDashboard size={24} color="#db5743" />
                            </div>
                            <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                Dashboard
                            </h1>
                        </div>

                        {showInformation ? (
                            /* Layout quando showInformation está ativo - barra de pesquisa no canto direito */
                            <div className="w-full flex flex-col lg:flex-row justify-between gap-6 mt-6">
                                <div className="flex-grow">
                                    <Collapsible>
                                        <div className="flex items-end gap-10">
                                            <div>
                                                <h3 className="text-sm font-semibold">
                                                    🎉 Confira as últimas atualizações!
                                                </h3>
                                                <span className="text-xs text-muted-foreground">Julho de 2025</span>
                                                <Separator className="my-2" />
                                                <p className="text-sm text-muted-foreground">
                                                    Confira as últimas atualizações do sistema. Clique para saber.
                                                </p>
                                            </div>
                                            <div className="flex gap-2">
                                                <CollapsibleTrigger asChild>
                                                    <Button variant="outline">
                                                        <ChevronsUpDown className="h-4 w-4" />
                                                    </Button>
                                                </CollapsibleTrigger>
                                                <Button
                                                    variant="outline"
                                                    onClick={() => {
                                                        setShowInformation(false);
                                                    }}
                                                >
                                                    <X className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <CollapsibleContent>
                                            <Card className="mt-4 border-t-0">
                                                <CardHeader>
                                                    <CardTitle>
                                                        O que há de novo?
                                                    </CardTitle>
                                                    <CardDescription>
                                                        Julho de 2025
                                                    </CardDescription>
                                                </CardHeader>
                                                <CardContent>
                                                    {userPermissions.isAdmin ? (
                                                        <UpdatesInformationAdmin />
                                                    ) : (
                                                        <UpdatesInformationOthers />
                                                    )}

                                                    <div className="mt-4 pt-3 border-t">
                                                        <p className="text-xs flex items-start gap-1">
                                                            <span>💡</span>
                                                            Mais funcionalidades serão adicionadas em breve. Fique atento às atualizações!
                                                        </p>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </CollapsibleContent>
                                    </Collapsible>
                                </div>

                                {/* Barra de pesquisa no canto direito quando showInformation está ativo */}
                                {clients.length > 0 && (
                                    <div className="lg:w-80 flex-shrink-0">
                                        <ClientSearchCommand
                                            clients={clients}
                                            onClientSelect={handleOpenClientNavigation}
                                        />
                                    </div>
                                )}
                            </div>
                        ) : (
                            /* Layout quando showInformation está removido - barra de pesquisa se expande */
                            clients.length > 0 && (
                                <div className="w-full mt-6">
                                    <div className="max-w-md">
                                        <ClientSearchCommand
                                            clients={clients}
                                            onClientSelect={handleOpenClientNavigation}
                                        />
                                    </div>
                                </div>
                            )
                        )}
                    </div>

                    <div className="flex flex-col items-end gap-4 flex-shrink-0">
                        <div className="flex-shrink-0">
                            <p className="text-sm font-medium">
                                Olá, {session?.user?.name?.split(" ")[0] || "Usuário"}! 👋🏼
                            </p>
                            <p className="text-xs text-zinc-600 dark:text-zinc-400">Hoje é {formattedDate}</p>
                        </div>
                        {(userPermissions.isDesigner || userPermissions.isCopy) && (
                            <PointClock />
                        )}
                    </div>
                </div>

                {session?.user?.email && (
                    <WelcomeAlert
                        userId={session.user.email}
                        userName={session.user.name?.split(" ")[0]}
                    />
                )}

                {userPermissions.canAccessDashboard ? (
                    <>
                        {userPermissions.isAdmin && (
                            <div className="flex flex-col lg:flex-row gap-6 mt-6">
                                <Card className="px-6 py-5 w-full lg:w-80 h-40 relative">
                                    <CardContent className="m-0 p-0 flex justify-between items-start">
                                        <div className="flex justify-between w-full">
                                            <div>
                                                <h3 className="text-sm">Total de clientes</h3>
                                                <span className="text-3xl font-bold">{clients.length}</span>
                                            </div>
                                            <Users size={18} className="text-primary2" />
                                        </div>
                                        <Link href="/clients">
                                            <Button variant="link" className="absolute bottom-2 right-3">
                                                Ver todos
                                                <MoveUpRight />
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                                <Card className="px-6 py-5 flex-grow h-40 relative">
                                    <CardContent className="m-0 p-0 flex justify-between items-start h-full">
                                        <div className="flex justify-between w-full">
                                            <div>
                                                <h3 className="text-sm">Clientes visualizados recentemente</h3>
                                                <ul className="mt-2">
                                                    {viewedClients.length > 0 ? (
                                                        viewedClients.map((client, index) => (
                                                            <li key={client.id ?? index} className="border-b text-sm py-1">
                                                                <button
                                                                    onClick={() => handleOpenClientNavigation(client.id)}
                                                                    className="text-blue-600 hover:underline text-left w-full"
                                                                >
                                                                    {client.name}
                                                                </button>
                                                            </li>
                                                        ))
                                                    ) : (
                                                        <p className="text-xs mt-8 text-zinc-500">Nenhum cliente visualizado recentemente.</p>
                                                    )}
                                                </ul>
                                            </div>
                                            <History size={18} className="text-primary2" />
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card className="px-6 py-5 flex-grow h-40 relative">
                                    <CardContent className="m-0 p-0 flex flex-col">
                                        <div className="flex justify-between w-full">
                                            <div>
                                                <h3 className="text-sm">Planejamentos criados em {currentMonth}</h3>
                                                {clients.some(client => (client?.monthlyPlannings?.length || 0) > 0) ? (
                                                    <span className="text-3xl font-bold">{clients.reduce((total, client) => {
                                                        return total + (client.monthlyPlannings || []).length;
                                                    }, 0)}</span>
                                                ) : (
                                                    <span className="text-3xl font-bold">0</span>
                                                )}
                                                {userPermissions.canCreatePlanning && (
                                                    <div className="absolute bottom-4 right-6">
                                                        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                                                            <DialogTrigger className="text-sm flex items-center gap-2 hover:underline cursor-pointer underline-offset-4 font-medium">
                                                                Novo planejamento
                                                                <CirclePlus size={18} />
                                                            </DialogTrigger>
                                                            <DialogContent>
                                                                <DialogHeader>
                                                                    <DialogTitle>Novo planejamento</DialogTitle>
                                                                </DialogHeader>
                                                                <div onFocus={(e) => e.stopPropagation()}>
                                                                    <CreatePlanning
                                                                        isShowTitle={false}
                                                                        onSuccess={refreshData}
                                                                        onClose={() => { handleClose() }}
                                                                    />
                                                                </div>
                                                            </DialogContent>
                                                        </Dialog>
                                                    </div>
                                                )}
                                            </div>
                                            <NotepadText size={18} className="text-primary2" />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        )}
                        {userPermissions.isAdmin ? (
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                                <Card className="px-6 py-5">
                                    <CardContent className="m-0 p-0 flex flex-col">
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm">Últimos planejamentos</p>
                                            <History size={18} className="text-primary2" />
                                        </div>
                                        {allClients.some(client => (client?.monthlyPlannings?.length || 0) > 0) ? (
                                            <ul className="mt-2">
                                                {allClients
                                                    .flatMap(client =>
                                                        (client.monthlyPlannings || []).map((planning: MonthlyPlanning) => ({
                                                            ...planning,
                                                            clientName: client.name,
                                                            clientId: client.id
                                                        }))
                                                    )
                                                    .sort((a, b) => {
                                                        return (b.createdAt ? new Date(b.createdAt).getTime() : 0) -
                                                            (a.createdAt ? new Date(a.createdAt).getTime() : 0);
                                                    })
                                                    .slice(0, 5)
                                                    .map((planning) => (
                                                        <li key={planning.id} className="text-sm py-1">
                                                            <Link href={`/monthly-planning/${planning.clientId}`} className="text-blue-600 hover:underline border-b py-1">
                                                                {planning.clientName} - {planning.month}/{planning.year}
                                                            </Link>
                                                        </li>
                                                    ))}
                                            </ul>
                                        ) : (
                                            <p className="flex justify-center items-center h-32 text-sm text-zinc-500">
                                                Nenhum planejamento criado.
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                                <Card className="px-6 py-5">
                                    <CardContent className="m-0 p-0">
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h3 className="text-sm">Relatórios de resultados criados em {currentMonth}</h3>
                                                {clients.some(client => (client?.resultsReport?.length || 0) > 0) ? (
                                                    <span className="text-3xl font-bold">{clients.reduce((total, client) => {
                                                        return total + (client.resultsReport || []).length;
                                                    }, 0)}</span>
                                                ) : (
                                                    <span className="text-3xl font-bold">0</span>
                                                )}
                                            </div>
                                            <FileChartColumn size={18} className="text-primary2" />
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card className="px-6 py-5">
                                    <CardContent className="m-0 p-0">
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm">
                                                Últimos relatórios de resultados
                                            </p>
                                            <History size={18} className="text-primary2" />
                                        </div>
                                        {allClients.some(client => (client?.resultsReport?.length || 0) > 0) ? (
                                            <ul className="mt-2">
                                                {allClients
                                                    .flatMap(client =>
                                                        (client.resultsReport || []).map((report: ResultsReport) => ({
                                                            ...report,
                                                            clientName: client.name,
                                                            clientId: client.id
                                                        }))
                                                    )
                                                    .sort((a, b) => {
                                                        return (b.createdAt ? new Date(b.createdAt).getTime() : 0) -
                                                            (a.createdAt ? new Date(a.createdAt).getTime() : 0);
                                                    })
                                                    .slice(0, 5)
                                                    .map((report) => (
                                                        <li key={report.id} className="text-sm py-1">
                                                            <Link href={`/results-report/${report.clientId}`} className="text-blue-600 hover:underline border-b py-1">
                                                                {report.clientName} - {report.month}/{report.year}
                                                            </Link>
                                                        </li>
                                                    ))}
                                            </ul>
                                        ) : (
                                            <p className="flex justify-center items-center h-32 text-sm text-zinc-500">
                                                Nenhum relatório de resultados criado.
                                            </p>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        ) : userPermissions.isContentCreator ? (
                            <div className="mt-6">
                                <Card className="px-6 py-5">
                                    <CardContent className="m-0 p-0">
                                        <div className="flex justify-between items-center mb-2">
                                            <p className="text-sm font-medium">
                                                Estatísticas de demandas
                                            </p>
                                            <ChartNoAxesGantt size={18} className="text-primary2" />
                                        </div>
                                        <UserDemandsStats />
                                    </CardContent>
                                </Card>
                            </div>
                        ) : null}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                            {userPermissions.isDesigner ? (
                                <>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm font-medium">
                                                    Estruturação do Feed
                                                </p>
                                                <Grid3x3 size={18} className="text-primary2" />
                                            </div>
                                            <FeedStructuringTasks />
                                        </CardContent>
                                    </Card>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm font-medium">
                                                    Minhas demandas
                                                </p>
                                                <ListTodo size={18} className="text-primary2" />
                                            </div>
                                            <UserLatestDemands />
                                        </CardContent>
                                    </Card>
                                </>
                            ) : userPermissions.isCopy ? (
                                <>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm font-medium">
                                                    Minhas demandas
                                                </p>
                                                <ListTodo size={18} className="text-primary2" />
                                            </div>
                                            <UserLatestDemands />
                                        </CardContent>
                                    </Card>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm">
                                                    Próximos eventos
                                                </p>
                                                <Calendar size={18} className="text-primary2" />
                                            </div>
                                            <div className="flex justify-center items-center my-44">
                                                <span className="text-sm text-zinc-500 dark:text-zinc-400">Em breve...</span>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </>
                            ) : (
                                <>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm">
                                                    Suas demandas
                                                </p>
                                                <ListTodo size={18} className="text-primary2" />
                                            </div>
                                            <UserLatestDemands />
                                        </CardContent>
                                    </Card>
                                    <Card className="px-6 py-5">
                                        <CardContent className="m-0 p-0">
                                            <div className="flex justify-between items-center">
                                                <p className="text-sm">
                                                    Demandas do time
                                                </p>
                                                <LayoutList size={18} className="text-primary2" />
                                            </div>
                                            <TotalPendingDemands />
                                        </CardContent>
                                    </Card>
                                </>
                            )}
                        </div>
                    </>
                ) : (
                    <Card className="mt-8">
                        <CardHeader>
                            <CardTitle className="text-lg">Acesso negado</CardTitle>
                            <CardDescription>
                                Você não tem permissão para acessar esta página ou seção. Por favor, entre em contato com o administrador do sistema.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Separator />
                            <p className="text-sm text-zinc-500 mt-8">
                                Apenas usuários com função de administrador podem acessar as estatísticas e informações completas do sistema.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
            <Footer />

            <ClientNavigationModal
                open={clientNavigationOpen}
                onOpenChange={setClientNavigationOpen}
                clientId={selectedClientId}
                clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
            />
        </div>
    );
}
