"use client"

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Clock, Edit, MoveLeft, SortAsc, SortDesc, Trash } from 'lucide-react';
import { Header } from '../../components/header';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Footer } from '../../components/footer';
import Loading from '../../components/ui/loading';
import { NotAllowed } from '../../components/not-allowed';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CreatePointRecord } from '../../components/create-point-record';
import { EditPointRecord } from '../../components/edit-point-record';
import { toast } from 'sonner';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/app/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog';

interface PointRecord {
    id: string;
    userId: string;
    userName: string;
    userEmail: string;
    clockIn?: string;
    clockOut?: string;
    date: string;
    totalHours?: number;
    timeBank?: number;
    createdAt: string;
}

export default function PointRecordPage() {
    const { data: session, status } = useSession();
    const [records, setRecords] = useState<PointRecord[]>([]);
    const [filteredRecords, setFilteredRecords] = useState<PointRecord[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortOption, setSortOption] = useState<'date_desc' | 'date_asc' | 'name_asc' | 'name_desc'>('date_desc');
    const [isAdmin, setIsAdmin] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [editingRecord, setEditingRecord] = useState<PointRecord | null>(null);
    const [timeBankData, setTimeBankData] = useState<{ userId: string, userName: string, userEmail: string, totalTimeBank: number }[]>([]);
    const [selectedDate, setSelectedDate] = useState<string>('');
    const [availableDates, setAvailableDates] = useState<string[]>([]);
    const router = useRouter();

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            const adminRoles = ["ADMIN", "DEVELOPER"];
                            setIsAdmin(user?.role ? adminRoles.includes(user.role) : false);
                        }
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router]);

    useEffect(() => {
        if (status === "authenticated" && isAdmin) {
            setIsLoading(true);
            Promise.all([
                fetch('/api/admin/point-record').then(res => res.json()),
                fetch('/api/admin/time-bank').then(res => res.json())
            ])
                .then(([recordsData, timeBankData]) => {
                    setRecords(recordsData);
                    setTimeBankData(timeBankData);

                    // Extrair datas únicas dos registros e ordenar
                    const uniqueDates = [...new Set(recordsData.map((record: PointRecord) => record.date))] as string[];
                    const sortedDates = uniqueDates.sort((a: string, b: string) => new Date(b).getTime() - new Date(a).getTime());
                    setAvailableDates(sortedDates);

                    // Definir data padrão como hoje se existir, senão a mais recente
                    const today = format(new Date(), 'yyyy-MM-dd');
                    const defaultDate = sortedDates.includes(today) ? today : sortedDates[0] || '';
                    setSelectedDate(defaultDate);
                })
                .catch((error) => {
                    console.error("Erro ao buscar dados:", error);
                })
                .finally(() => setIsLoading(false));
        }
    }, [status, isAdmin]);

    useEffect(() => {
        let result = [...records];

        // Filtrar por data selecionada
        if (selectedDate) {
            result = result.filter(record => record.date === selectedDate);
        }

        // Filtrar por busca
        if (searchQuery.trim() !== '') {
            const query = searchQuery.toLowerCase();
            result = result.filter(record =>
                record.userName?.toLowerCase().includes(query) ||
                record.userEmail?.toLowerCase().includes(query)
            );
        }

        // Ordenar
        switch (sortOption) {
            case 'date_desc':
                result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                break;
            case 'date_asc':
                result.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
                break;
            case 'name_asc':
                result.sort((a, b) => a.userName.localeCompare(b.userName));
                break;
            case 'name_desc':
                result.sort((a, b) => b.userName.localeCompare(a.userName));
                break;
        }

        setFilteredRecords(result);
    }, [records, searchQuery, sortOption, selectedDate]);

    const handleDeleteRecord = async (recordId: string) => {
        const response = await fetch(`/api/admin/point-record/${recordId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error('Erro ao excluir registro');
        }
        toast.success('Registro excluído com sucesso!');
        refreshData();
    };

    const refreshData = () => {
        if (isAdmin) {
            setIsLoading(true);
            Promise.all([
                fetch('/api/admin/point-record').then(res => res.json()),
                fetch('/api/admin/time-bank').then(res => res.json())
            ])
                .then(([recordsData, timeBankData]) => {
                    setRecords(recordsData);
                    setTimeBankData(timeBankData);

                    // Atualizar datas disponíveis
                    const uniqueDates = [...new Set(recordsData.map((record: PointRecord) => record.date))] as string[];
                    const sortedDates = uniqueDates.sort((a: string, b: string) => new Date(b).getTime() - new Date(a).getTime());
                    setAvailableDates(sortedDates);

                    // Manter data selecionada se ainda existir, senão usar hoje ou a mais recente
                    const today = format(new Date(), 'yyyy-MM-dd');
                    if (!sortedDates.includes(selectedDate)) {
                        const defaultDate = sortedDates.includes(today) ? today : sortedDates[0] || '';
                        setSelectedDate(defaultDate);
                    }
                })
                .catch((error) => {
                    console.error("Erro ao buscar dados:", error);
                })
                .finally(() => setIsLoading(false));
        }
    };

    const formatTime = (timeString?: string) => {
        if (!timeString) return '-';
        return format(new Date(timeString), 'HH:mm', { locale: ptBR });
    };

    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        return format(date, 'dd/MM/yyyy', { locale: ptBR });
    };

    const formatHours = (hours?: number) => {
        if (!hours) return '-';
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        return `${h}h ${m}m`;
    };

    const formatDateButton = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        const today = new Date();

        // Verificar se é hoje
        if (date.toDateString() === today.toDateString()) {
            return 'Hoje';
        }

        // Verificar se é ontem
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        if (date.toDateString() === yesterday.toDateString()) {
            return 'Ontem';
        }

        // Formato padrão
        return format(date, 'dd/MM', { locale: ptBR });
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className='flex flex-col xs:flex-row gap-4 justify-between xs:items-end mb-4'>
                            <div className="flex flex-col items-start gap-2">
                                <Button variant="outline" onClick={() => router.push("/panel")}>
                                    <MoveLeft />
                                </Button>
                                <div className="flex items-center gap-2 group">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Registro de ponto
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <Clock size={24} color="#db5743" />
                                    </div>
                                </div>
                            </div>
                            <CreatePointRecord onSuccess={refreshData} />
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 justify-between mb-4">
                            <div className='w-full'>
                                <div className="w-full max-w-lg">
                                    <Input
                                        type="text"
                                        placeholder="Pesquisar por nome ou email"
                                        className="placeholder:text-sm"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                    />
                                </div>
                                {availableDates.length > 0 && (
                                    <div className="mt-8">
                                        <h3 className="text-sm font-medium mb-3">Selecionar dia:</h3>
                                        <div className="relative">
                                            <div className="overflow-x-auto scrollbar-hide">
                                                <div className="flex gap-2 pb-2" style={{ width: 'max-content' }}>
                                                    {availableDates.map((date, index) => (
                                                        <Button
                                                            key={date}
                                                            variant={selectedDate === date ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => setSelectedDate(date)}
                                                            className={`flex-shrink-0 min-w-[80px] ${index >= 8 ? 'ml-2' : ''}`}
                                                        >
                                                            {formatDateButton(date)}
                                                        </Button>
                                                    ))}
                                                </div>
                                            </div>
                                            {availableDates.length > 8 && (
                                                <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent pointer-events-none" />
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className='flex gap-2 flex-col'>
                                <div className="">
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button>
                                                Ver banco de horas ({
                                                    timeBankData.length === 0 ? 'nenhum colaborador' :
                                                        timeBankData.length === 1 ? `${timeBankData.length} colaborador` : `${timeBankData.length} colaboradores`
                                                })
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-2xl">
                                            <DialogHeader>
                                                <DialogTitle>Banco de horas por colaborador</DialogTitle>
                                            </DialogHeader>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                                                {timeBankData
                                                    .sort((a, b) => b.totalTimeBank - a.totalTimeBank)
                                                    .map((user) => (
                                                        <div key={user.userId} className="p-3 border rounded">
                                                            <div className="font-medium">{user.userName}</div>
                                                            <div className="text-sm text-muted-foreground">{user.userEmail}</div>
                                                            <div className="text-lg font-bold text-green-600 mt-1">
                                                                {formatHours(user.totalTimeBank)}
                                                            </div>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                            {timeBankData.length === 0 && (
                                                <p className="text-center text-muted-foreground mb-4">Nenhum colaborador com saldo de horas</p>
                                            )}
                                        </DialogContent>
                                    </Dialog>
                                </div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                                            {sortOption.includes('date')
                                                ? <span>Ordenar por data</span>
                                                : <span>Ordenar por nome</span>
                                            }
                                            {sortOption.includes('asc') ? <SortAsc size={16} /> : <SortDesc size={16} />}
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem
                                            onClick={() => setSortOption('date_desc')}
                                            className={sortOption === 'date_desc' ? 'bg-muted' : ''}
                                        >
                                            <span className="pr-2">Mais recentes</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('date_asc')}>
                                            <span className="pr-2">Mais antigos</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={() => setSortOption('name_asc')}>
                                            <span className="pr-2">Nome (A-Z)</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('name_desc')}>
                                            <span className="pr-2">Nome (Z-A)</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                        </div>

                        <Table>
                            <TableCaption>Total de registros: {filteredRecords.length}</TableCaption>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Data</TableHead>
                                    <TableHead>Usuário</TableHead>
                                    <TableHead>Entrada</TableHead>
                                    <TableHead>Saída</TableHead>
                                    <TableHead>Total de horas</TableHead>
                                    <TableHead>Editar</TableHead>
                                    <TableHead>Excluir</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredRecords.length > 0 ? (
                                    filteredRecords.map((record) => (
                                        <TableRow key={record.id}>
                                            <TableCell>{formatDate(record.date)}</TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{record.userName}</div>
                                                    <div className="text-sm text-muted-foreground">{record.userEmail}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>{formatTime(record.clockIn)}</TableCell>
                                            <TableCell>{formatTime(record.clockOut)}</TableCell>
                                            <TableCell>{formatHours(record.totalHours)}</TableCell>
                                            <TableCell>
                                                <Dialog open={editingRecord?.id === record.id} onOpenChange={(open) => !open && setEditingRecord(null)}>
                                                    <DialogTrigger asChild>
                                                        <Button variant="outline" size="icon" onClick={() => setEditingRecord(record)}>
                                                            <Edit size={16} />
                                                        </Button>
                                                    </DialogTrigger>
                                                    {editingRecord && (
                                                        <EditPointRecord
                                                            record={editingRecord}
                                                            onSuccess={refreshData}
                                                            onClose={() => setEditingRecord(null)}
                                                        />
                                                    )}
                                                </Dialog>
                                            </TableCell>
                                            <TableCell>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="outline" size="icon">
                                                            <Trash size={16} color="red" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Tem certeza que deseja excluir este registro?</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Esta ação não pode ser desfeita.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                            <AlertDialogAction onClick={() => handleDeleteRecord(record.id)}>Continuar</AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={7} className="text-center py-6">
                                            Nenhum registro encontrado
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </>
                ) : (
                    <NotAllowed page='/panel' />
                )}
            </div>
            <Footer />
        </div>
    );
}