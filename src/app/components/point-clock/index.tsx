"use client"

import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { CalendarPlus2, Clock, LogIn, LogOut } from 'lucide-react';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';
import { Separator } from '../ui/separator';

interface PointRecord {
    id?: string;
    clockIn: string | null;
    clockOut: string | null;
    date?: string;
    hasOpenEntry: boolean;
    totalHours?: number;
}

interface PreviousRecord {
    id: string;
    clockIn: string;
    clockOut: string;
    totalHours: number;
}

export const PointClock = () => {
    const { data: session } = useSession();
    const [open, setOpen] = useState(false);
    const [todayRecord, setTodayRecord] = useState<PointRecord>({ clockIn: null, clockOut: null, hasOpenEntry: false });
    const [isLoading, setIsLoading] = useState(false);
    const [previousRecords, setPreviousRecords] = useState<PreviousRecord[]>([]);
    const [currentTime, setCurrentTime] = useState(new Date());

    const today = new Date().toISOString().split('T')[0];
    const totalHours = previousRecords.reduce((acc, record) => acc + record.totalHours, 0) + (todayRecord.totalHours || 0);

    const getElapsedTime = () => {
        if (!todayRecord.hasOpenEntry || !todayRecord.clockIn) return null;
        const entryTime = new Date(todayRecord.clockIn);
        const elapsed = (currentTime.getTime() - entryTime.getTime()) / 1000;
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = Math.floor(elapsed % 60);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    useEffect(() => {
        if (session?.user?.email) {
            fetchTodayRecord();
        }
    }, [session]);
    
    useEffect(() => {
        if (open && session?.user?.email) {
            fetchTodayRecord();
        }
    }, [open, session]);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (todayRecord.hasOpenEntry) {
            interval = setInterval(() => {
                setCurrentTime(new Date());
            }, 1000);
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [todayRecord.hasOpenEntry]);

    const fetchTodayRecord = async () => {
        try {
            const response = await fetch(`/api/point-record/today`);
            if (response.ok) {
                const data = await response.json();
                setTodayRecord(data || { clockIn: null, clockOut: null, hasOpenEntry: false });
                setPreviousRecords(data.previousRecords || []);
            }
        } catch (error) {
            console.error('Erro ao buscar registro:', error);
        }
    };

    const handleClockAction = async (action: 'in' | 'out') => {
        setIsLoading(true);
        try {
            const response = await fetch('/api/point-record', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action,
                    date: today
                }),
            });

            if (!response.ok) {
                throw new Error('Erro ao registrar ponto');
            }

            const data = await response.json();
            if (action === 'in') {
                setTodayRecord({ ...data, hasOpenEntry: true });
            } else {
                setTodayRecord({ ...data, hasOpenEntry: false });
                fetchTodayRecord();
            }
            toast.success(action === 'in' ? 'Entrada registrada!' : 'Saída registrada!');
        } catch (error) {
            console.error('Erro:', error);
            toast.error('Erro ao registrar ponto');
        } finally {
            setIsLoading(false);
        }
    };

    const formatTime = (timeString?: string | null) => {
        if (!timeString) return '--:--';
        return new Date(timeString).toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <div className='flex gap-2 items-start'>
                    {todayRecord.hasOpenEntry && getElapsedTime() && (
                        <div className="text-center p-2 bg-green-50 dark:bg-green-950 rounded-lg w-fit">
                            <p className="text-sm font-mono font-bold text-green-600">
                                {getElapsedTime()}
                            </p>
                        </div>
                    )}
                    <div>
                        <Button variant="secondary" className="flex gap-2">
                            <Clock />
                            {todayRecord.hasOpenEntry ? `Entrada às ${formatTime(todayRecord.clockIn)}` : 'Registre seu ponto'}
                        </Button>
                        <p className="text-xs text-muted-foreground text-right mt-1">
                            Clique para ver detalhes
                        </p>
                    </div>
                </div>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <CalendarPlus2 size={16} />
                        Registro de ponto
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">
                            {new Date().toLocaleDateString('pt-BR', {
                                weekday: 'long',
                                day: 'numeric',
                                month: 'long'
                            })}
                        </p>
                        <p className="text-2xl font-mono font-bold">
                            {new Date().toLocaleTimeString('pt-BR', {
                                hour: '2-digit',
                                minute: '2-digit'
                            })}
                        </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
                        <div className="text-center">
                            <p className="text-xs text-muted-foreground">Entrada</p>
                            <p className="font-mono font-semibold">
                                {formatTime(todayRecord.clockIn)}
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-xs text-muted-foreground">Saída</p>
                            <p className="font-mono font-semibold">
                                {formatTime(todayRecord.clockOut)}
                            </p>
                        </div>
                    </div>

                    {todayRecord.hasOpenEntry && getElapsedTime() && (
                        <div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                            <p className="text-xs text-muted-foreground mb-1">Tempo trabalhado</p>
                            <p className="text-xl font-mono font-bold text-green-600">
                                {getElapsedTime()}
                            </p>
                        </div>
                    )}

                    <div className="flex gap-2">
                        <Button
                            onClick={() => handleClockAction('in')}
                            disabled={isLoading || todayRecord.hasOpenEntry}
                            className="flex-1 flex gap-2"
                        >
                            <LogIn size={16} />
                            {todayRecord.hasOpenEntry ? 'Entrada em aberto' : 'Registrar entrada'}
                        </Button>

                        <Button
                            onClick={() => handleClockAction('out')}
                            disabled={isLoading || !todayRecord.hasOpenEntry}
                            className="flex-1 flex gap-2"
                        >
                            <LogOut size={16} />
                            Registrar saída
                        </Button>
                    </div>

                    {previousRecords.length > 0 && (
                        <>
                            <Separator />
                            <div>
                                <p className='text-sm font-medium mb-2 text-muted-foreground'>
                                    Registros anteriores de hoje ({previousRecords.length})
                                </p>
                                <div className="space-y-2">
                                    {previousRecords.map((record) => (
                                        <div key={record.id} className="flex justify-between items-center p-2 bg-muted rounded text-sm">
                                            <div className="flex gap-4">
                                                <span className="font-mono">{formatTime(record.clockIn)}</span>
                                                <span className="text-muted-foreground">→</span>
                                                <span className="font-mono">{formatTime(record.clockOut)}</span>
                                            </div>
                                            <span className="text-xs text-muted-foreground">
                                                {Math.floor(record.totalHours)}h {Math.round((record.totalHours % 1) * 60)}m
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className='flex justify-end items-center gap-2'>
                                <p className='text-sm font-medium text-muted-foreground'>
                                    Total de horas hoje:
                                </p>
                                <span className='font-bold'>
                                    {Math.floor(totalHours)}h {Math.round((totalHours % 1) * 60)}m
                                </span>
                            </div>
                        </>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};