"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Ellipsis } from "lucide-react";
import { toast } from "sonner";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/app/components/ui/select";
import { renderDeliveryItem } from "./render-delivery-item";
import { GenerateDeliveriesPDF } from "@/app/components/generate-deliveries-pdf";
import { ActivityInfo } from "../activity-info";
import { AddFeedUrlModal } from "../add-feed-url-modal";
import { Separator } from "../ui/separator";

interface Content {
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption?: string;
    status?: string;
    copywriting?: string;
    reference?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    urlFolder?: string;
}

interface WeeklyActivity {
    id: string;
    description: string;
    week: number;
    contents: Content[];
}

interface MonthlyPlanning {
    status: string;
    id: string;
    month: number;
    year: number;
    activities: WeeklyActivity[];
}

interface ClientProps {
    id: string;
    name: string;
    instagramUsername?: string | null;
    [key: string]: unknown;
}

interface DeliveriesContentProps {
    clientId: string;
    refreshTrigger?: number;
    client?: ClientProps | null;
    isPdfMode?: boolean;
    initialMonth?: string;
    compactView?: boolean;
    initialCaptionFilter?: "todos" | "com-legenda" | "sem-legenda";
    selectedItemsForPdf?: string[];
}

export const DeliveriesContent = ({
    clientId,
    refreshTrigger = 0,
    client,
    isPdfMode = false,
    initialMonth,
    compactView = false,
    initialCaptionFilter = "todos",
    selectedItemsForPdf = []
}: DeliveriesContentProps) => {
    const [plannedContents, setPlannedContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [filteredContents, setFilteredContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [availableMonths, setAvailableMonths] = useState<string[]>([]);
    const [selectedMonth, setSelectedMonth] = useState<string | null>(initialMonth || null);
    const [captionFilter, setCaptionFilter] = useState<"todos" | "com-legenda" | "sem-legenda">(initialCaptionFilter);
    const [photoFilter, setPhotoFilter] = useState<"todos" | "com-foto" | "sem-foto">("todos");
    const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

    const toggleItemSelection = (itemId: string) => {
        setSelectedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    const toggleSelectAll = () => {
        if (selectedItems.size === filteredContents.length) {
            setSelectedItems(new Set());
        } else {
            setSelectedItems(new Set(filteredContents.map(content => content.id)));
        }
    };

    const clearSelection = () => {
        setSelectedItems(new Set());
    };

    useEffect(() => {
        if (!clientId) return;

        const fetchData = async () => {
            try {
                setIsLoading(true);
                const response = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents`);

                if (!response.ok) {
                    throw new Error(`Erro ao buscar dados do cliente: ${response.status}`);
                }

                const data = await response.json();

                const allPlannedContents: Array<Content & { planningId: string, week: number, month: number, year: number }> = [];
                const monthsSet = new Set<string>();

                if (data.monthlyPlannings && Array.isArray(data.monthlyPlannings)) {
                    data.monthlyPlannings.forEach((planning: MonthlyPlanning) => {
                        let hasPlannedContent = false;
                        const monthKey = `${planning.month}-${planning.year}`;

                        if (planning.status === "aprovado") {
                            planning.activities.forEach((activity: WeeklyActivity) => {
                                if (activity.contents && Array.isArray(activity.contents)) {
                                    activity.contents.forEach((content: Content) => {
                                        allPlannedContents.push({
                                            ...content,
                                            planningId: planning.id,
                                            week: activity.week,
                                            month: planning.month,
                                            year: planning.year
                                        });
                                        hasPlannedContent = true;
                                    });
                                }
                            });

                            if (hasPlannedContent) {
                                monthsSet.add(monthKey);
                            }
                        }
                    });
                }

                const sortedMonths = Array.from(monthsSet).sort((a, b) => {
                    const [monthA, yearA] = a.split('-').map(Number);
                    const [monthB, yearB] = b.split('-').map(Number);

                    if (yearA !== yearB) return yearB - yearA;
                    return monthB - monthA;
                });

                setAvailableMonths(sortedMonths);
                setPlannedContents(allPlannedContents);

                if (!selectedMonth && sortedMonths.length > 0) {
                    setSelectedMonth(sortedMonths[0]);
                }

                if (selectedMonth && !sortedMonths.includes(selectedMonth) && sortedMonths.length > 0) {
                    setSelectedMonth(sortedMonths[0]);
                }
            } catch (error) {
                console.error("Erro ao buscar dados do cliente:", error);
                toast.error("Erro ao carregar dados do cliente");
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [clientId, refreshTrigger, selectedMonth]);

    useEffect(() => {
        if (selectedMonth) {
            const [month, year] = selectedMonth.split('-').map(Number);

            let filtered = plannedContents.filter(content =>
                content.month === month && content.year === year
            );

            if (captionFilter === "com-legenda") {
                filtered = filtered.filter(content => !!content.caption);
            } else if (captionFilter === "sem-legenda") {
                filtered = filtered.filter(content => !content.caption);
            }

            if (photoFilter === "com-foto") {
                filtered = filtered.filter(content => content.urlStructuringFeed && content.urlStructuringFeed.length > 0);
            } else if (photoFilter === "sem-foto") {
                filtered = filtered.filter(content => !content.urlStructuringFeed || content.urlStructuringFeed.length === 0);
            }

            const sorted = [...filtered].sort((a, b) => {
                return new Date(a.activityDate).getTime() - new Date(b.activityDate).getTime();
            });

            setFilteredContents(sorted);
            setSelectedItems(new Set());
        } else {
            setFilteredContents([]);
            setSelectedItems(new Set());
        }
    }, [selectedMonth, plannedContents, captionFilter, photoFilter]);

    const handleUrlUpdate = (contentId: string, newUrls: string[], newUrlTypes: string[], newMediaTypes?: string[], newUrlFolder?: string) => {
        setPlannedContents(prevContents =>
            prevContents.map(content =>
                content.id === contentId
                    ? {
                        ...content,
                        urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
                        urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
                        urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
                        urlFolder: newUrlFolder
                    }
                    : content
            )
        );

        setFilteredContents(prevContents =>
            prevContents.map(content =>
                content.id === contentId
                    ? {
                        ...content,
                        urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
                        urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
                        urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
                        urlFolder: newUrlFolder
                    }
                    : content
            )
        );
    };

    const renderContent = (content: Content & { planningId: string, week: number, month: number, year: number }) => {
        const formattedDate = format(new Date(content.activityDate), 'dd/MM/yyyy', { locale: ptBR });

        return (
            <Card key={content.id} className="flex flex-col h-full relative">
                {!isPdfMode && (
                    <div className="px-3 mt-2 flex items-center justify-between">
                        <Checkbox
                            checked={selectedItems.has(content.id)}
                            onCheckedChange={() => toggleItemSelection(content.id)}
                        />
                        <span className="font-medium text-xs text-muted-foreground">#{content.id.substring(0, 8).toUpperCase()}</span>
                    </div>
                )}
                <CardHeader className="p-3">
                    <div className="flex flex-wrap items-center justify-between gap-0">
                        <div className="flex flex-col sm:flex-row items-center gap-2">
                            <h3 className="font-medium text-base">{content.contentType}</h3>
                            <Badge variant="outline" className="font-medium text-xs px-2.5 py-1">
                                {formattedDate}
                            </Badge>
                            <Badge variant="secondary" className="font-medium">{content.destination}</Badge>
                        </div>
                        <div className="flex items-center gap-1">
                            <ActivityInfo
                                activity={{
                                    ...content,
                                    url: Array.isArray(content.urlStructuringFeed)
                                        ? content.urlStructuringFeed
                                        : content.urlStructuringFeed
                                            ? [content.urlStructuringFeed]
                                            : undefined
                                }}
                                localCaption={content.caption}
                                localCopywriting={content.copywriting}
                                localReference={content.reference}
                            />
                            <AddFeedUrlModal
                                contentId={content.id}
                                currentUrl={content.urlStructuringFeed}
                                currentUrlTypes={content.urlTypes}
                                currentMediaTypes={content.urlMediaTypes}
                                currentUrlFolder={content.urlFolder}
                                onUrlUpdated={handleUrlUpdate}
                                type="content"
                            />
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="py-2 px-3 flex-grow">
                    {renderDeliveryItem(content, false, compactView)}
                </CardContent>
                <CardFooter className="px-3 py-3 border-t">
                    <div className="w-full">
                        <h4 className="text-sm font-semibold text-gray-800 dark:text-white mb-2">Legenda:</h4>
                        {content.caption ? (
                            <p className="text-sm text-gray-700 dark:text-gray-300 break-words leading-relaxed">{content.caption}</p>
                        ) : (
                            <p className="text-sm text-gray-500 dark:text-gray-400 italic">Sem legenda disponível</p>
                        )}
                    </div>
                </CardFooter>
            </Card>
        );
    };

    return (
        <Card className="w-full">
            <CardHeader className="px-6 py-3 sticky top-0 z-10 bg-background dark:bg-background rounded-t-xl">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                    {!isPdfMode && (
                        <div className="flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto">
                            {availableMonths.length > 0 && (
                                <Select
                                    value={selectedMonth || undefined}
                                    onValueChange={setSelectedMonth}
                                >
                                    <SelectTrigger className="w-full sm:w-[180px]">
                                        <SelectValue placeholder="Selecione o mês" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableMonths.map(month => {
                                            const [m, y] = month.split('-');
                                            const monthName = format(new Date(Number(y), Number(m) - 1, 1), 'MMMM', { locale: ptBR });
                                            const formattedMonth = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)}/${y}`;

                                            return (
                                                <SelectItem key={month} value={month}>
                                                    {formattedMonth}
                                                </SelectItem>
                                            );
                                        })}
                                    </SelectContent>
                                </Select>
                            )}

                            <Select
                                value={photoFilter}
                                onValueChange={(value) => setPhotoFilter(value as "todos" | "com-foto" | "sem-foto")}
                            >
                                <SelectTrigger className="w-full sm:w-[180px]">
                                    <SelectValue placeholder="Filtrar por foto" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="todos">Com e sem foto</SelectItem>
                                    <SelectItem value="com-foto">Com foto</SelectItem>
                                    <SelectItem value="sem-foto">Sem foto</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select
                                value={captionFilter}
                                onValueChange={(value) => setCaptionFilter(value as "todos" | "com-legenda" | "sem-legenda")}
                            >
                                <SelectTrigger className="w-full sm:w-[180px]">
                                    <SelectValue placeholder="Filtrar por legenda" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="todos">Com e sem legenda</SelectItem>
                                    <SelectItem value="com-legenda">Com legenda</SelectItem>
                                    <SelectItem value="sem-legenda">Sem legenda</SelectItem>
                                </SelectContent>
                            </Select>

                            {filteredContents.length > 0 && (
                                <div className="flex items-center gap-2">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="select-all"
                                            checked={selectedItems.size === filteredContents.length && filteredContents.length > 0}
                                            onCheckedChange={toggleSelectAll}
                                        />
                                        <label
                                            htmlFor="select-all"
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                        >
                                            Selecionar todos ({selectedItems.size}/{filteredContents.length})
                                        </label>
                                    </div>
                                    {selectedItems.size > 0 && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={clearSelection}
                                        >
                                            Limpar seleção
                                        </Button>
                                    )}
                                </div>
                            )}

                            {selectedItems.size > 0 && (
                                <GenerateDeliveriesPDF
                                    clientId={clientId}
                                    clientName={client?.name || "Cliente"}
                                    month={selectedMonth || undefined}
                                    disabled={selectedItems.size === 0}
                                    captionFilter={captionFilter}
                                    selectedItems={Array.from(selectedItems)}
                                />
                            )}
                        </div>
                    )}
                </div>
            </CardHeader>

            <CardContent className={`${isPdfMode ? 'p-0' : ''} pt-4`}>
                {filteredContents.length > 0 ? (
                    <>
                        {isPdfMode ? (
                            <div className="pdf-container">
                                {(() => {
                                    const contentsToRender = selectedItemsForPdf.length > 0
                                        ? filteredContents.filter(content => selectedItemsForPdf.includes(content.id))
                                        : filteredContents;

                                    type DemandGroup = Content & {
                                        planningId: string;
                                        week: number;
                                        month: number;
                                        year: number;
                                        feedImages: string[];
                                        storyImages: string[];
                                        feedIndices: number[];
                                        storyIndices: number[];
                                    };

                                    const demands = contentsToRender.reduce((acc, content) => {
                                        const demandId = content.id;
                                        if (!acc[demandId]) {
                                            acc[demandId] = {
                                                ...content,
                                                feedImages: [],
                                                storyImages: [],
                                                feedIndices: [],
                                                storyIndices: [],
                                            };
                                        }

                                        const allUrls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [content.urlStructuringFeed] : []);
                                        const allTypes = content.urlTypes || [];

                                        allUrls.forEach((url, i) => {
                                            const type = allTypes[i] || 'feed';
                                            if (type === 'feed') {
                                                if (acc[demandId].feedImages.length < 6) {
                                                    acc[demandId].feedImages.push(url);
                                                    acc[demandId].feedIndices.push(i);
                                                }
                                            } else {
                                                if (acc[demandId].storyImages.length < 6) {
                                                    acc[demandId].storyImages.push(url);
                                                    acc[demandId].storyIndices.push(i);
                                                }
                                            }
                                        });

                                        return acc;
                                    }, {} as Record<string, DemandGroup>);

                                    return Object.values(demands).map(demand => (
                                        <div key={demand.id} className="pdf-demand-group">
                                            <div className="pdf-demand-header">
                                                <div className="pdf-content-type">{demand.contentType}</div>
                                                <div className="pdf-date">{format(new Date(demand.activityDate), 'dd/MM/yyyy', { locale: ptBR })}</div>
                                                {demand.destination && <div className="pdf-destination">Destino: {demand.destination}</div>}
                                                {demand.urlFolder && (
                                                    <div className="pdf-folder-link">
                                                        <a href={demand.urlFolder} target="_blank" rel="noopener noreferrer" className="pdf-folder-button">
                                                            📁 Acessar pasta no Google Drive
                                                        </a>
                                                    </div>
                                                )}
                                                {/* Dados ocultos para detecção de vídeo no PDF */}
                                                <div className="pdf-url-media-types" style={{ display: 'none' }}>
                                                    {demand.urlMediaTypes ? JSON.stringify(demand.urlMediaTypes) : '[]'}
                                                </div>
                                            </div>
                                            {(demand.feedImages.length + demand.storyImages.length === 1) ? (
                                                <div className="pdf-single-image-row" style={{ width: '100%', height: 'auto', minHeight: '400px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                                    {demand.feedImages.map((url: string, index: number) => {
                                                        const originalIndex = demand.feedIndices[index];
                                                        return (
                                                            <div key={`feed-${demand.id}-${index}`} style={{ width: 'auto', maxWidth: '400px', height: 'auto', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                                {renderDeliveryItem({
                                                                    ...demand,
                                                                    urlStructuringFeed: demand.urlStructuringFeed,
                                                                    urlMediaTypes: demand.urlMediaTypes,
                                                                    urlTypes: demand.urlTypes,
                                                                    currentUrlIndex: originalIndex
                                                                }, true, true)}
                                                            </div>
                                                        );
                                                    })}
                                                    {demand.storyImages.map((url: string, index: number) => {
                                                        const originalIndex = demand.storyIndices[index];
                                                        return (
                                                            <div key={`story-${demand.id}-${index}`} style={{ width: 'auto', maxWidth: '400px', height: 'auto', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                                {renderDeliveryItem({
                                                                    ...demand,
                                                                    urlStructuringFeed: demand.urlStructuringFeed,
                                                                    urlMediaTypes: demand.urlMediaTypes,
                                                                    urlTypes: demand.urlTypes,
                                                                    currentUrlIndex: originalIndex
                                                                }, true, true)}
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            ) : (
                                                <div>
                                                    {/* Linha das imagens de Feed */}
                                                    {demand.feedImages.length > 0 && (
                                                        <div className="pdf-grid" style={{ marginBottom: '16px' }}>
                                                            {demand.feedImages.map((_, index: number) => {
                                                                const originalIndex = demand.feedIndices[index];
                                                                return (
                                                                    <div key={`feed-${demand.id}-${index}`} className="pdf-card" data-content-type="feed">
                                                                        {renderDeliveryItem({
                                                                            ...demand,
                                                                            urlStructuringFeed: demand.urlStructuringFeed,
                                                                            urlMediaTypes: demand.urlMediaTypes,
                                                                            urlTypes: demand.urlTypes,
                                                                            currentUrlIndex: originalIndex
                                                                        }, true, true)}
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    )}

                                                    {/* Linha das imagens de Story */}
                                                    {demand.storyImages.length > 0 && (
                                                        <div className="pdf-grid">
                                                            {demand.storyImages.map((_, index: number) => {
                                                                const originalIndex = demand.storyIndices[index];
                                                                return (
                                                                    <div key={`story-${demand.id}-${index}`} className="pdf-card" data-content-type="story">
                                                                        {renderDeliveryItem({
                                                                            ...demand,
                                                                            urlStructuringFeed: demand.urlStructuringFeed,
                                                                            urlMediaTypes: demand.urlMediaTypes,
                                                                            urlTypes: demand.urlTypes,
                                                                            currentUrlIndex: originalIndex
                                                                        }, true, true)}
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                            {demand.caption && (
                                                <div className="pdf-caption-container">
                                                    <div className="pdf-caption">
                                                        <span>Legenda:</span>
                                                        <p className="pdf-caption-text">{demand.caption}</p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ));
                                })()}
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {filteredContents.map(content => renderContent(content))}
                            </div>
                        )}
                    </>
                ) : (
                    <div className="text-center text-sm">
                        {isLoading ? (
                            <Ellipsis />
                        ) : (
                            <div className="pb-8">
                                <Separator className="my-6" />
                                <p className="mt-8">Nenhuma entrega encontrada. Altere os filtros de busca.</p>
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
