"use client"

import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { CirclePlus } from 'lucide-react';
import { toast } from 'sonner';

interface User {
    id: string;
    name: string;
    email: string;
}

interface CreatePointRecordProps {
    onSuccess: () => void;
}

export const CreatePointRecord = ({ onSuccess }: CreatePointRecordProps) => {
    const [open, setOpen] = useState(false);
    const [users, setUsers] = useState<User[]>([]);
    const [selectedUserId, setSelectedUserId] = useState('');
    const [date, setDate] = useState('');
    const [clockIn, setClockIn] = useState('');
    const [clockOut, setClockOut] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (open) {
            fetch('/api/users')
                .then(res => res.json())
                .then(data => setUsers(data))
                .catch(error => console.error('Erro ao buscar usuários:', error));
        }
    }, [open]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!selectedUserId || !date) {
            toast.error('Usuário e data são obrigatórios');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch('/api/admin/point-record', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: selectedUserId,
                    date,
                    clockIn: clockIn ? `${date}T${clockIn}:00` : null,
                    clockOut: clockOut ? `${date}T${clockOut}:00` : null
                }),
            });

            if (!response.ok) {
                throw new Error('Erro ao criar registro');
            }

            toast.success('Registro criado com sucesso!');
            setOpen(false);
            setSelectedUserId('');
            setDate('');
            setClockIn('');
            setClockOut('');
            onSuccess();
        } catch (error) {
            console.error('Erro:', error);
            toast.error('Erro ao criar registro');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button className='bg-primary2 hover:bg-orange-600 text-white'>
                    <CirclePlus />
                    Novo registro
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Criar registro de ponto</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <Label htmlFor="user">Usuário</Label>
                        <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um usuário" />
                            </SelectTrigger>
                            <SelectContent>
                                {users.map(user => (
                                    <SelectItem key={user.id} value={user.id}>
                                        {user.name} ({user.email})
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label htmlFor="date">Data</Label>
                        <Input
                            id="date"
                            type="date"
                            value={date}
                            onChange={(e) => setDate(e.target.value)}
                        />
                    </div>
                    <div>
                        <Label htmlFor="clockIn">Horário de entrada</Label>
                        <Input
                            id="clockIn"
                            type="time"
                            value={clockIn}
                            onChange={(e) => setClockIn(e.target.value)}
                            placeholder="Horário de entrada"
                        />
                    </div>
                    <div>
                        <Label htmlFor="clockOut">Horário de saída</Label>
                        <Input
                            id="clockOut"
                            type="time"
                            value={clockOut}
                            onChange={(e) => setClockOut(e.target.value)}
                            placeholder="Horário de saída"
                        />
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                            Cancelar
                        </Button>
                        <Button type="submit" disabled={isLoading}>
                            {isLoading ? 'Criando' : 'Criar registro'}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};