import { useState, useEffect } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";

interface WelcomeAlertProps {
    userId: string;
    userName?: string;
    version?: string;
}

export function WelcomeAlert({ userId, userName, version = "1.0" }: WelcomeAlertProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const seenVersion = localStorage.getItem(`welcome-seen-${userId}`);

        if (!seenVersion || seenVersion !== version) {
            setIsOpen(true);
        }
    }, [userId, version]);

    const handleClose = () => {
        localStorage.setItem(`welcome-seen-${userId}`, version);
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>
                        ⚙️ Nova funcionalidade
                    </DialogTitle>
                    <DialogDescription className="pt-2">
                        Olá <span className="font-medium">{userName || "usuário"}</span>, confira as últimas atualizações!
                    </DialogDescription>
                </DialogHeader>
                <Separator />
                <div className="text-sm text-zinc-700 dark:text-zinc-300 space-y-4">  
                    <p>
                        Agora você pode registrar suas horas de trabalho registrando seu ponto.
                        Você tem opção de registrar entrada e saída, além de visualizar seu total de horas diárias.
                    </p>
                    <p>
                        Em breve você poderá visualizar seu total de horas semanais e mensais, e também seu banco de horas.
                    </p>
                </div>

                <DialogFooter>
                    <Button onClick={handleClose} className="w-full sm:w-auto">
                        Entendi
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}