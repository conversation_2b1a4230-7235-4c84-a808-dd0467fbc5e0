"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "./ui/button";
import { MoveUpRight, Calendar } from "lucide-react";
import { Separator } from "./ui/separator";

export function TotalPendingDemands() {
    const [totalPendingDemands, setTotalPendingDemands] = useState<number | null>(null);
    const [totalPendingContentDemands, setTotalPendingContentDemands] = useState<number | null>(null);
    const [totalPendingGeneralDemands, setTotalPendingGeneralDemands] = useState<number | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [currentMonth, setCurrentMonth] = useState<string>("");

    useEffect(() => {
        const monthNames = [
            "Janeiro", "Fevereiro", "Março", "Abril", 
            "Maio", "Jun<PERSON>", "Jul<PERSON>", "Agosto", 
            "Set<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dezembro"
        ];
        const now = new Date();
        setCurrentMonth(monthNames[now.getMonth()]);
        
        const fetchPendingDemands = async () => {
            try {
                const currentMonth = now.getMonth() + 1;
                const currentYear = now.getFullYear();
                
                const response = await fetch(`/api/demands/pending-count?month=${currentMonth}&year=${currentYear}`);
                if (response.ok) {
                    const data = await response.json();
                    setTotalPendingDemands(data.count);
                    setTotalPendingContentDemands(data.contentDemands);
                    setTotalPendingGeneralDemands(data.generalDemands);
                } else {
                    console.error('Erro ao buscar demandas pendentes');
                }
            } catch (error) {
                console.error('Erro ao carregar demandas pendentes:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchPendingDemands();
    }, []);

    if (isLoading) {
        return (
            <div className="animate-pulse mt-4 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-3/4"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
            </div>
        );
    }

    if (totalPendingDemands === 0) {
        return (
            <div className="flex justify-center items-center my-44">
                <p className="text-sm text-zinc-500">Nenhuma demanda pendente em {currentMonth}</p>
            </div>
        );
    }

    return (
        <div>
            <div className="space-y-4 mt-2">
                <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                        <span className="text-3xl font-bold">{totalPendingDemands !== null ? totalPendingDemands : "0"}</span>
                        <Calendar className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <span className="text-sm text-muted-foreground">Demandas pendentes em {currentMonth}</span>
                </div>

                <Separator className="my-2" />

                <div className="grid grid-cols-2 gap-4 pt-2">
                    <div className="flex flex-col">
                        <span className="text-2xl font-semibold">
                            {totalPendingContentDemands !== null ? totalPendingContentDemands : "0"}
                        </span>
                        <span className="text-sm text-muted-foreground">Demandas de conteúdo</span>
                    </div>

                    <div className="flex flex-col">
                        <span className="text-2xl font-semibold">
                            {totalPendingGeneralDemands !== null ? totalPendingGeneralDemands : "0"}
                        </span>
                        <span className="text-sm text-muted-foreground">Demandas pontuais</span>
                    </div>
                </div>
            </div>

            <Separator className="my-8" />

            <div className="flex justify-between mt-16 relative p-0">
                <Link href="/admin/demands">
                    <Button variant="link" className="absolute bottom-0 right-0">
                        Ver todas
                        <MoveUpRight className="h-4 w-4" />
                    </Button>
                </Link>
            </div>
        </div>
    )
}
